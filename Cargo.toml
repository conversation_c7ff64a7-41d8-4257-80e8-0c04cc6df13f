[package]
name = "heartbeatdemo"
version = "0.1.0"
edition = "2024"

[[bin]]
name = "client"
path = "src/client.rs"

[[bin]]
name = "server"
path = "src/server.rs"

[dependencies]
tonic = "0.13.1"
prost = "0.13.5"
tokio = { version = "1.0", features = ["rt-multi-thread", "time", "macros"] }
tokio-stream = "0.1"
chrono = "0.4"
tracing = "0.1"
tracing-subscriber = "0.3"

[build-dependencies]
tonic-build = "0.13.1"
