use tonic::{Request, Response, Status};
use tracing::{info, error};
use crate::protoes::service1::{Request as Service1Request, Response as Service1Response, service1_client::Service1Client};
use crate::heartbeatclient::HeartbeatClient;
// use crate::proto::service1::service1_client::Service1Client;
// use crate::proto::service1::{Request as Service1Request, Response as Service1Response};
// use super::heartbeat::HeartbeatClient;

pub struct Service1ClientImpl {
    client: Service1Client<tonic::transport::Channel>,
    heartbeat: HeartbeatClient,
}

impl Service1ClientImpl {
    pub async fn new(
        endpoint: &str,
        client_id: &str,
        service_name: &str,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        let heartbeat = HeartbeatClient::new(endpoint, client_id.to_string(), service_name.to_string()).await;
        let channel = tonic::transport::Channel::from_shared(endpoint.to_string())
            .map_err(|e| format!("Invalid endpoint: {}", e))?
            .connect()
            .await
            .map_err(|e| format!("Connection failed: {}", e))?;
        let client = Service1Client::new(channel);

        Ok(Self { client, heartbeat })
    }

    pub async fn do_something(
        &mut self,
        data: String,
    ) -> Result<Response<Service1Response>, Status> {
        if !self.heartbeat.is_healthy() {
            error!("{} is unhealthy, cannot process request", self.heartbeat.service_name);
            return Err(Status::unavailable(format!(
                "{} is unhealthy, please try again later",
                self.heartbeat.service_name
            )));
        }

        let request = Request::new(Service1Request { data });
        info!("Sending request to {}: {:?}", self.heartbeat.service_name, request);
        self.client.do_something(request).await
    }

    pub fn is_healthy(&self) -> bool {
        self.heartbeat.is_healthy()
    }
}