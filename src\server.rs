use heartbeatdemo::{heartbeatimpl::HeartbeatServiceImpl, protoes::{heartbeat::heartbeat_service_server::HeartbeatServiceServer, service1::{service1_server::{Service1, Service1Server}, Request as Service1Request, Response as Service1Response}}};
use tonic::{Request, Response, Status};
use tracing::{info, Level};
use tracing_subscriber::FmtSubscriber;


// mod proto {
//     pub mod heartbeat;
//     pub mod service1;
// }
// mod heartbeat;

#[derive(Debug)]
struct Service1Impl;

#[tonic::async_trait]
impl Service1 for Service1Impl {
    async fn do_something(
        &self,
        request: Request<Service1Request>,
    ) -> Result<Response<Service1Response>, Status> {
        let data = request.into_inner().data;
        info!("Processing request: data={}", data);
        Ok(Response::new(Service1Response {
            result: format!("Service1 processed: {}", data),
        }))
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化 tracing
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;

    let addr = "0.0.0.0:50051".parse()?;
    let service1 = Service1Impl;
    let heartbeat_service = HeartbeatServiceImpl;

    info!("Starting Service1 server on {}", addr);

    tonic::transport::Server::builder()
        .add_service(Service1Server::new(service1))
        .add_service(HeartbeatServiceServer::new(heartbeat_service))
        .serve(addr)
        .await?;

    Ok(())
}