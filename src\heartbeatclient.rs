use chrono::Utc;
use crate::protoes::heartbeat::{HeartbeatRequest, HeartbeatResponse, heartbeat_service_client::HeartbeatServiceClient};
use tokio::time::{interval, timeout, Duration};
use tokio_stream::{
    wrappers::{IntervalStream, ReceiverStream},
    StreamExt,
};
use tonic::{Request, Status, Streaming};
use tracing::{error, info, warn};

#[derive(Clone)]
pub struct HeartbeatClient {
    client_id: String,
    pub service_name: String,
    is_healthy: tokio::sync::watch::Receiver<bool>,
    // 不持有 client，因為重連會創建新 client
}

impl HeartbeatClient {
    pub async fn new(
        endpoint: &str,
        client_id: String,
        service_name: String,
        send_heartbeat: bool, // 控制是否發送客戶端心跳
    ) -> Self {
        let (is_healthy, client_id_clone, service_name_clone) = Self::start_heartbeat_monitor(
            endpoint.to_string(),
            client_id.clone(),
            service_name.clone(),
            send_heartbeat,
        ).await;

        Self {
            client_id: client_id_clone,
            service_name: service_name_clone,
            is_healthy,
        }
    }

    async fn start_heartbeat_monitor(
        endpoint: String,
        client_id: String,
        service_name: String,
        send_heartbeat: bool,
    ) -> (tokio::sync::watch::Receiver<bool>, String, String) {
        let (tx, rx) = tokio::sync::watch::channel(false);

        let client_id_clone = client_id.clone();
        let service_name_clone = service_name.clone();
        tokio::spawn(async move {
            loop {
                info!(service = %service_name, endpoint, "Attempting to connect");
                match tonic::transport::Channel::from_shared(endpoint.clone()) {
                    Ok(channel) => match channel.connect().await {
                        Ok(channel) => {
                            let mut client = HeartbeatServiceClient::new(channel);
                            info!(service = %service_name, "Connection established");
                            if Self::monitor_heartbeat(&mut client, client_id.clone(), service_name.clone(), tx.clone(), send_heartbeat).await.is_ok() {
                                info!(service = %service_name, "Heartbeat monitor completed successfully");
                            }
                        }
                        Err(e) => {
                            error!(service = %service_name, "Connection failed: {}", e);
                            let _ = tx.send(false);
                        }
                    },
                    Err(e) => {
                        error!(service = %service_name, "Invalid endpoint: {}", e);
                        let _ = tx.send(false);
                    }
                }
                warn!(service = %service_name, "Retrying connection in 2 seconds");
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        });

        (rx, client_id:client_id_clone, service_name)
    }

    async fn monitor_heartbeat(
        client: &mut HeartbeatServiceClient<tonic::transport::Channel>,
        client_id: String,
        service_name: String,
        tx: tokio::sync::watch::Sender<bool>,
        send_heartbeat: bool,
    ) -> Result<(), Status> {
        // 創建客戶端心跳流（即使不發送也要初始化流）
        let (tx_stream, rx_stream) = tokio::sync::mpsc::channel(100);

        // 如果啟用客戶端心跳，發送心跳消息
        if send_heartbeat {
            let client_id_clone = client_id.clone();
            tokio::spawn(async move {
                let mut interval = interval(Duration::from_secs(5));
                // 發送初始心跳以建立流
                if tx_stream
                    .send(HeartbeatRequest {
                        client_id: client_id_clone.clone(),
                        timestamp: Utc::now().timestamp(),
                    })
                    .await
                    .is_err()
                {
                    warn!(client_id = %client_id_clone, "Initial heartbeat send failed");
                    return;
                }

                loop {
                    interval.tick().await;
                    let timestamp = Utc::now().timestamp();
                    info!(client_id = %client_id_clone, timestamp, "Sending heartbeat");
                    if tx_stream
                        .send(HeartbeatRequest {
                            client_id: client_id_clone.clone(),
                            timestamp,
                        })
                        .await
                        .is_err()
                    {
                        warn!(client_id = %client_id_clone, "Heartbeat send channel closed");
                        break;
                    }
                }
            });
        } else {
            // 發送初始心跳以建立流
            let _ = tx_stream.send(HeartbeatRequest {
                client_id: client_id.clone(),
                timestamp: Utc::now().timestamp(),
            }).await;
        }

        let request = Request::new(Streaming::new(rx_stream));
        let mut server_stream = client.heartbeat_stream(request).await?.into_inner();
        let heartbeat_timeout = Duration::from_secs(10);

        let _ = tx.send(true); // 連接成功，設置健康狀態

        while let Ok(Some(response)) = timeout(heartbeat_timeout, server_stream.message()).await {
            match response {
                Ok(Some(response)) => {
                    info!(
                        service = %service_name,
                        timestamp = response.timestamp,
                        status = response.status,
                        "Received heartbeat"
                    );
                    let _ = tx.send(true);
                }
                Ok(None) => {
                    warn!(service = %service_name, "Heartbeat stream closed by server");
                    let _ = tx.send(false);
                    return Err(Status::unavailable("Server closed stream"));
                }
                Err(e) => {
                    error!(service = %service_name, "Heartbeat error: {}", e);
                    let _ = tx.send(false);
                    return Err(e);
                }
            }
        }

        error!(service = %service_name, "Heartbeat timeout, server may be down");
        let _ = tx.send(false);
        Err(Status::deadline_exceeded("Heartbeat timeout"))
    }

    pub fn is_healthy(&self) -> bool {
        *self.is_healthy.borrow()
    }
}