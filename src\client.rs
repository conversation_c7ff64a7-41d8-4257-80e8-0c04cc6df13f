use heartbeatdemo::service1client::Service1ClientImpl;
// use crate::service1client::Service1ClientImpl;
use tracing::{*, Level};
use tracing_subscriber::FmtSubscriber;


#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 初始化 tracing
    let subscriber = FmtSubscriber::builder()
        .with_max_level(Level::INFO)
        .finish();
    tracing::subscriber::set_global_default(subscriber)?;

    let mut service1_client = Service1ClientImpl::new(
        "http://127.0.0.1:50051",
        "client_1",
        "Service1",
    ).await?;

    // 測試業務調用
    loop {
        match service1_client.do_something("test data".to_string()).await {
            Ok(response) => info!("Service1 response: {:?}", response.into_inner().result),
            Err(e) => error!("Service1 error: {}", e),
        }
        info!("Service1 is healthy: {}", service1_client.is_healthy());
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
    }
}